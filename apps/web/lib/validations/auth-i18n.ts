import { z } from "zod";

// Type for translation function
type TranslationFunction = (
  key: string,
  values?: Record<string, unknown>
) => string;

export function createLoginSchema(t: TranslationFunction) {
  return z.object({
    email: z
      .string()
      .min(1, t("auth.validation.emailRequired"))
      .refine(
        (value) => {
          // Allow either email format or username (no @ symbol)
          return value.includes("@")
            ? z.string().email().safeParse(value).success
            : value.length >= 3;
        },
        {
          message: t("auth.validation.emailInvalid"),
        }
      ),
    password: z
      .string()
      .min(6, t("auth.validation.passwordMinLength", { min: 6 }))
      .max(100, t("auth.validation.passwordMaxLength", { max: 100 })),
  });
}

export function createRegisterSchema(t: TranslationFunction) {
  return z
    .object({
      email: z
        .string()
        .min(1, t("auth.validation.emailRequiredSimple"))
        .email(t("auth.validation.emailInvalidSimple")),
      password: z
        .string()
        .min(8, t("auth.validation.passwordMinLength", { min: 8 }))
        .max(100, t("auth.validation.passwordMaxLength", { max: 100 }))
        .regex(
          /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/,
          t("auth.validation.passwordComplexity")
        ),
      confirmPassword: z.string(),
      firstName: z
        .string()
        .min(1, t("auth.validation.firstNameRequired"))
        .max(50, t("auth.validation.firstNameMaxLength", { max: 50 })),
      lastName: z
        .string()
        .min(1, t("auth.validation.lastNameRequired"))
        .max(50, t("auth.validation.lastNameMaxLength", { max: 50 })),
    })
    .refine((data) => data.password === data.confirmPassword, {
      message: t("auth.validation.passwordsNoMatch"),
      path: ["confirmPassword"],
    });
}

export function createForgotPasswordSchema(t: TranslationFunction) {
  return z.object({
    email: z
      .string()
      .min(1, t("auth.validation.emailRequiredSimple"))
      .email(t("auth.validation.emailInvalidSimple")),
  });
}

export function createResetPasswordSchema(t: TranslationFunction) {
  return z
    .object({
      password: z
        .string()
        .min(8, t("auth.validation.passwordMinLength", { min: 8 }))
        .max(100, t("auth.validation.passwordMaxLength", { max: 100 }))
        .regex(
          /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/,
          t("auth.validation.passwordComplexity")
        ),
      confirmPassword: z.string(),
      token: z.string().min(1, t("auth.validation.resetTokenRequired")),
    })
    .refine((data) => data.password === data.confirmPassword, {
      message: t("auth.validation.passwordsNoMatch"),
      path: ["confirmPassword"],
    });
}

// Export types
export type LoginFormData = z.infer<ReturnType<typeof createLoginSchema>>;
export type RegisterFormData = z.infer<ReturnType<typeof createRegisterSchema>>;
export type ForgotPasswordFormData = z.infer<
  ReturnType<typeof createForgotPasswordSchema>
>;
export type ResetPasswordFormData = z.infer<
  ReturnType<typeof createResetPasswordSchema>
>;
