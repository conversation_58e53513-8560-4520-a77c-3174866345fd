"use client";

import { zod<PERSON>esolver } from "@hookform/resolvers/zod";
import { Loader2 } from "lucide-react";
import { useTranslations } from "next-intl";
import * as React from "react";
import { useForm } from "react-hook-form";

import { But<PERSON> } from "@workspace/ui/components/button";
import { Form } from "@workspace/ui/components/form";
import { RHFInput } from "@workspace/ui/components/rhf";
import {
  createForgotPasswordSchema,
  ForgotPasswordFormData,
} from "../schemas/auth.schem";

interface ForgotPasswordFormProps {
  onSubmit?: (data: ForgotPasswordFormData) => Promise<void> | void;
  isLoading?: boolean;
  error?: string | null;
  success?: boolean;
}

export function ForgotPasswordForm({
  onSubmit,
  isLoading = false,
  error,
  success = false,
}: ForgotPasswordFormProps) {
  const [isSubmitting, setIsSubmitting] = React.useState(false);
  const t = useTranslations();

  const forgotPasswordSchema = React.useMemo(
    () => createForgotPasswordSchema(t),
    [t]
  );

  const form = useForm<ForgotPasswordFormData>({
    resolver: zodResolver(forgotPasswordSchema),
    defaultValues: {
      email: "",
    },
  });

  const handleSubmit = async (data: ForgotPasswordFormData) => {
    if (!onSubmit) return;

    try {
      setIsSubmitting(true);
      await onSubmit(data);
    } catch (error) {
      console.error("Forgot password error:", error);
    } finally {
      setIsSubmitting(false);
    }
  };

  if (success) {
    return (
      <div className="space-y-4">
        <div className="rounded-md bg-green-50 p-4 text-center">
          <div className="text-green-800">
            <h3 className="text-sm font-medium">
              {t("auth.forgotPassword.form.successTitle")}
            </h3>
            <p className="mt-2 text-sm">
              {t("auth.forgotPassword.form.successMessage")}
            </p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4">
        {error && (
          <div className="rounded-md bg-destructive/15 p-3 text-sm text-destructive">
            {error}
          </div>
        )}

        <RHFInput
          control={form.control}
          name="email"
          placeholder={t("auth.forgotPassword.form.emailPlaceholder")}
          type="email"
          disabled={isLoading || isSubmitting}
        />

        <Button
          type="submit"
          variant="primary"
          className="w-full"
          disabled={isLoading || isSubmitting}
        >
          {isLoading || isSubmitting ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              {t("auth.forgotPassword.form.submittingButton")}
            </>
          ) : (
            t("auth.forgotPassword.form.submitButton")
          )}
        </Button>
      </form>
    </Form>
  );
}
