"use client";

import { zod<PERSON><PERSON>olver } from "@hookform/resolvers/zod";
import { Loader2 } from "lucide-react";
import * as React from "react";
import { useForm } from "react-hook-form";
import { useTranslations } from "next-intl";

import { <PERSON><PERSON> } from "@workspace/ui/components/button";
import { Form } from "@workspace/ui/components/form";
import { RHFInput, RHFPasswordInput } from "@workspace/ui/components/rhf";
import {
  createLoginSchema,
  type LoginFormData,
} from "@/lib/validations/auth-i18n";

interface LoginFormProps {
  onSubmit?: (data: LoginFormData) => Promise<void> | void;
  isLoading?: boolean;
  error?: string | null;
}

export function LoginForm({
  onSubmit,
  isLoading = false,
  error,
}: LoginFormProps) {
  const [isSubmitting, setIsSubmitting] = React.useState(false);
  const t = useTranslations();

  const loginSchema = React.useMemo(() => createLoginSchema(), [t]);

  const form = useForm<LoginFormData>({
    resolver: zodResolver(loginSchema),
    defaultValues: {
      email: "",
      password: "",
    },
  });

  const handleSubmit = async (data: LoginFormData) => {
    if (!onSubmit) return;

    try {
      setIsSubmitting(true);
      await onSubmit(data);
    } catch (error) {
      console.error("Login error:", error);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4">
        {error && (
          <div className="rounded-md bg-destructive/15 p-3 text-sm text-destructive">
            {error}
          </div>
        )}

        <RHFInput
          control={form.control}
          name="email"
          placeholder={t("auth.login.form.emailPlaceholder")}
          disabled={isLoading || isSubmitting}
        />

        <RHFPasswordInput
          control={form.control}
          name="password"
          placeholder={t("auth.login.form.passwordPlaceholder")}
          disabled={isLoading || isSubmitting}
        />

        <Button
          type="submit"
          variant="primary"
          className="w-full"
          disabled={isLoading || isSubmitting}
        >
          {isLoading || isSubmitting ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              {t("auth.login.form.submittingButton")}
            </>
          ) : (
            t("auth.login.form.submitButton")
          )}
        </Button>
      </form>
    </Form>
  );
}
