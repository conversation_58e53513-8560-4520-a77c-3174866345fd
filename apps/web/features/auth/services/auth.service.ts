import {
  LoginFormData,
  RegisterFormData,
} from "@/features/auth/schemas/auth.schem";
import { axiosInstance } from "@workspace/api-client";

class AuthService {
  static async login(data: LoginFormData) {
    return axiosInstance.post("/auth/login", data);
  }

  static async register(data: RegisterFormData) {
    return axiosInstance.post("/auth/register", data);
  }
}

export default AuthService;
