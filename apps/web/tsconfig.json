{"extends": "@workspace/typescript-config/nextjs.json", "compilerOptions": {"baseUrl": ".", "paths": {"@/*": ["./*"], "@workspace/ui/*": ["../../packages/ui/src/*"], "@workspace/api-client/*": ["../../packages/api-client/src/*"]}, "plugins": [{"name": "next"}]}, "include": ["next-env.d.ts", "next.config.ts", "middleware.ts", "i18n/*.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts"], "exclude": ["node_modules"]}